<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <title>技术支持内容编辑器</title>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <link rel="stylesheet" href="/admin/css/bootstrap.min.css"/>
    <link rel="stylesheet" href="/admin/css/bootstrap-responsive.min.css"/>
    <link rel="stylesheet" href="/admin/css/matrix-style.css"/>
    <link rel="stylesheet" href="/admin/css/matrix-media.css"/>

    <style>
        /* 隐藏TinyMCE的状态栏 */
        .tox-statusbar {
            display: none !important;
        }

        /* 确保全屏模式下完全覆盖整个屏幕 */
        .fullscreen-mode .tox-fullscreen {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            z-index: 99999 !important;
            background: #fff !important;
        }

        /* 全屏模式下隐藏所有其他内容 */
        .fullscreen-mode body > *:not(.tox-fullscreen) {
            display: none !important;
        }

        /* 确保TinyMCE工具栏在全屏模式下正确显示 */
        .tox-fullscreen .tox-menubar {
            position: relative !important;
            z-index: 1 !important;
        }

        .tox-fullscreen .tox-toolbar {
            position: relative !important;
            z-index: 1 !important;
        }
    </style>

    <link href="/admin/font-awesome/css/font-awesome.css" rel="stylesheet"/>
    <link rel="stylesheet" href="/admin/css/jquery.gritter.css"/>

    <script src="/admin/js/jquery.min.js"></script>
    <script src="/admin/js/jquery.cookie.js"></script>
    <script src="tinymce.min.js"></script>
</head>
<body>

<script src="/admin/js/head.js"></script>

<!--main-container-part-->
<div id="content">
    <!--breadcrumbs-->
    <div id="content-header">
        <div id="breadcrumb">
            <a href="#" class="current" id="support-name">技术支持内容详情</a>
        </div>
    </div>
    <!--End-breadcrumbs-->

    <!--Action boxes-->
    <div class="container-fluid">
        <div class="widget-box">
            <div class="widget-content tab-content">
                <!-- 工具栏 -->
                <div id="page-toolbar" class="page-toolbar" style="margin-bottom: 15px;">
                    <a class="btn btn-primary" href="/admin/support_content.html">
                        <i class="icon-arrow-left"></i> 返回列表
                    </a>
                    <a class="btn btn-primary" href="javascript:update_detail()">
                        <i class="icon-save"></i> 保存
                    </a>
                    <select class="input-medium" style="margin-bottom: 0;width: auto" id="sel_status">
                        <option value="1">状态：已发布</option>
                        <option value="0">状态：草稿</option>
                    </select>
                    <button type="button" class="btn btn-success" onclick="generateSupportPage()">
                        <i class="icon icon-th"></i> 生成预览页
                    </button>
                    <button type="button" class="btn btn-warning" onclick="replaceSupportImages()">
                        <i class="icon icon-picture"></i> 优化图片
                    </button>
                    <button type="button" class="btn btn-primary" onclick="update_detail()">
                        <i class="icon icon-save"></i> 保存内容
                    </button>
                </div>

                <!-- 支持内容信息显示 -->
                <div id="support-info" class="alert alert-info" style="margin-bottom: 15px; display: none;">
                    <h4 id="support-title">支持内容标题</h4>
                    <p><strong>内容ID:</strong> <span id="support-id"></span></p>
                    <p><strong>类型:</strong> <span id="support-type">详情</span></p>
                    <p><strong>创建时间:</strong> <span id="created-time"></span></p>
                </div>

                <!-- 隐藏字段 -->
                <input id="current-support-id" style="display: none">
                <input id="old_content" style="display: none">

                <!-- TinyMCE编辑器 -->
                <div id="tinymce_demo"></div>
            </div>
        </div>
    </div>

    <!-- 生成预览页面模态框 -->
    <div id="makeHtml" class="modal hide fade" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
        <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            <h3 id="myModalLabel">生成预览页面</h3>
        </div>
        <div class="modal-body">
            <div class="control-group">
                <label class="control-label">内容标题</label>
                <div class="controls">
                    <input type="text" id="preview-title" class="input-xlarge" placeholder="输入内容标题">
                </div>
            </div>
            <div class="control-group">
                <label class="control-label">内容类型</label>
                <div class="controls">
                    <input type="text" id="preview-type" class="input-xlarge" value="技术支持" placeholder="输入内容类型">
                </div>
            </div>
            <div class="control-group">
                <label class="control-label">语言</label>
                <div class="controls">
                    <select id="preview-lang" class="input-medium">
                        <option value="0">中文</option>
                        <option value="1">英文</option>
                    </select>
                </div>
            </div>
            <p style="color: #666; margin-top: 15px;">
                <i class="icon-info-sign"></i>
                将根据当前编辑的内容生成技术支持预览页面
            </p>
        </div>
        <div class="modal-footer">
            <a class="btn btn-danger" href="javascript:make_support_html()">确认生成</a>
            <a data-dismiss="modal" class="btn">取消</a>
        </div>
    </div>
</div>

<!--end-main-container-part-->
<script src="/admin/js/footer.js"></script>
<script src="/admin/js/jquery.ui.custom.js"></script>
<script src="/admin/js/bootstrap.min.js"></script>
<script src="/admin/js/jquery.gritter.min.js"></script>
<script src="/admin/js/matrix.js"></script>

<script>
// 全局变量
let currentSupportId = null;
let currentSupportTitle = null;
let currentSupportData = null;
let editorInitialized = false;

// 页面加载完成后初始化
$(document).ready(function() {
    // 从URL参数或Cookie获取支持内容标识（支持ID和标题两种方式）
    const urlParams = new URLSearchParams(window.location.search);
    const urlSupportId = urlParams.get('id');
    const urlSupportTitle = urlParams.get('title');
    const cookieSupportId = $.cookie('support_content_id');
    const cookieSupportTitle = $.cookie('support_content_title');

    // 详细的调试信息
    console.log('=== 技术支持内容编辑器调试信息 ===');
    console.log('完整URL:', window.location.href);
    console.log('URL参数字符串:', window.location.search);
    console.log('URL中的id:', urlSupportId);
    console.log('URL中的title:', urlSupportTitle);
    console.log('Cookie中的support_content_id:', cookieSupportId);
    console.log('Cookie中的support_content_title:', cookieSupportTitle);

    // 优先级：URL参数 > Cookie，标题优先于ID（参考微信文章编辑器）
    if (urlSupportTitle) {
        // URL中有标题参数，优先使用标题方式
        console.log('使用URL中的支持内容标题方式加载:', urlSupportTitle);
        currentSupportTitle = decodeURIComponent(urlSupportTitle);
        console.log('解码后的标题:', currentSupportTitle);
        // 清除旧的支持内容ID Cookie，设置标题Cookie
        $.removeCookie('support_content_id');
        $.cookie('support_content_title', currentSupportTitle, { expires: 1 });
        loadSupportDataByTitle();
    } else if (urlSupportId && urlSupportId !== 'null' && urlSupportId !== 'undefined' && !isNaN(urlSupportId)) {
        // URL中有支持内容ID参数
        console.log('使用URL中的支持内容ID方式加载:', urlSupportId);
        currentSupportId = parseInt(urlSupportId);
        $.cookie('support_content_id', currentSupportId, { expires: 1 });
        loadSupportData();
    } else if (cookieSupportTitle) {
        // Cookie中有标题
        console.log('使用Cookie中的支持内容标题方式加载:', cookieSupportTitle);
        currentSupportTitle = cookieSupportTitle;
        loadSupportDataByTitle();
    } else if (cookieSupportId && cookieSupportId !== 'null' && cookieSupportId !== 'undefined' && !isNaN(cookieSupportId)) {
        // Cookie中有支持内容ID
        console.log('使用Cookie中的支持内容ID方式加载:', cookieSupportId);
        currentSupportId = parseInt(cookieSupportId);
        loadSupportData();
    } else {
        console.log('错误：没有找到支持内容ID或标题');
        show_gitter('提示信息', '缺少支持内容ID或标题参数，请从内容列表进入', 2);
        setTimeout(() => {
            window.location.href = '/admin/support_content.html';
        }, 2000);
    }
    console.log('========================');
});

// 简化稳定的TinyMCE配置（参考wechat_article_detail.html）
tinymce.init({
    selector: '#tinymce_demo', //容器，可使用css选择器
    language: 'zh_CN', //调用放在langs文件夹内的语言包
    toolbar: true, //工具栏
    menubar: true, //菜单栏
    branding: false, //右下角技术支持
    inline: false, //开启内联模式
    elementpath: false,
    min_height: 400, //最小高度
    height: 800,  //高度
    skin: 'oxide',
    theme: 'silver',
    theme_url: './theme.min.js', // 指定正确的主题文件路径
    toolbar_sticky: true,
    visualchars_default_state: true, //显示不可见字符
    image_caption: true,
    paste_data_images: true,
    relative_urls: false,
    removed_menuitems: 'newdocument',  //清除"文件"菜单
    plugins: "lists,hr, advlist,anchor,autolink,autoresize,charmap,code,codesample,emoticons,fullscreen,image,media,insertdatetime,link,pagebreak,paste,preview,print,searchreplace,table,textcolor,toc,visualchars,wordcount", //依赖lists插件
    toolbar: 'bullist numlist anchor charmap emoticons fullscreen hr image insertdatetime link media pagebreak paste preview print searchreplace textcolor wordcount',
    //选中时出现的快捷工具，与插件有依赖关系
    images_upload_url: '/apis/upload_pic/', /*后图片上传接口*/ /*返回值为json类型 {'location':'uploads/jpg'}*/

    // 自定义图片上传处理函数（解决JSON格式问题）
    images_upload_handler: function (blobInfo, success, failure) {
        var xhr, formData;

        xhr = new XMLHttpRequest();
        xhr.withCredentials = false;
        xhr.open('POST', '/apis/upload_pic/');

        xhr.onload = function() {
            var json;

            if (xhr.status != 200) {
                failure('HTTP Error: ' + xhr.status);
                return;
            }

            try {
                json = JSON.parse(xhr.responseText);
            } catch (e) {
                failure('Invalid JSON: ' + xhr.responseText);
                return;
            }

            if (json.status === 'ok' && json.path) {
                // 转换我们的响应格式为TinyMCE期望的格式
                success(json.path);
            } else {
                failure(json.msg || '上传失败');
            }
        };

        formData = new FormData();
        formData.append('file', blobInfo.blob(), blobInfo.filename());

        xhr.send(formData);
    },

    // 样式和格式保存相关配置
    keep_styles: true, // 保持样式
    verify_html: false, // 不验证HTML，允许所有标签和属性
    cleanup: false, // 不清理HTML
    convert_urls: false, // 不转换URL
    remove_script_host: false, // 不移除脚本主机

    // 允许所有HTML元素和属性
    valid_elements: '*[*]', // 允许所有元素和所有属性
    valid_children: '+body[style],+div[style],+p[style],+span[style],+h1[style],+h2[style],+h3[style],+h4[style],+h5[style],+h6[style],+img[style],+a[style]',
    extended_valid_elements: '*[*]', // 扩展有效元素

    // 保留样式属性
    custom_elements: '~style',

    // 粘贴设置 - 保留格式
    paste_retain_style_properties: 'all', // 保留所有样式属性
    paste_remove_styles: false, // 不移除样式
    paste_remove_styles_if_webkit: false, // WebKit下也不移除样式
    paste_strip_class_attributes: 'none', // 不移除class属性

    // 内容过滤设置
    allow_conditional_comments: true,
    allow_html_data_urls: true,

    init_instance_callback: 'initSupportData',
    setup: function (editor) {
        editor.on('change', function () {
            editor.save();
        });

        // 添加全屏模式事件监听
        editor.on('FullscreenStateChanged', function (e) {
            if (e.state) {
                // 进入全屏模式
                document.body.classList.add('fullscreen-mode');
                console.log('进入全屏模式');
            } else {
                // 退出全屏模式
                document.body.classList.remove('fullscreen-mode');
                console.log('退出全屏模式');
            }
        });

        // 添加内容变化监听，确保样式被保存
        editor.on('NodeChange', function () {
            editor.save();
        });

        // 添加粘贴后处理，确保样式被保留
        editor.on('PastePostProcess', function (e) {
            console.log('粘贴内容:', e.node.innerHTML);
            // 不做任何处理，保留原始格式
        });
    }
});

// 加载支持内容数据（参考微信文章详情页面的方式）
function loadSupportData() {
    if (!currentSupportId) return;

    $.ajax({
        url: '/apis/support_detail/',
        type: 'GET',
        data: { id: currentSupportId },
        success: function(response) {
            if (response.status === 'ok') {
                currentSupportData = response.data;
                updateSupportPageInfo(response.data);
            } else {
                show_gitter('错误', response.msg || '加载支持内容失败', 2);
            }
        },
        error: function() {
            show_gitter('错误', '网络错误，加载支持内容失败', 2);
        }
    });
}

// 通过支持内容标题加载数据
function loadSupportDataByTitle() {
    if (!currentSupportTitle) return;

    $.ajax({
        url: '/apis/get_support_by_title/',
        type: 'POST',
        data: { title: currentSupportTitle, type: '详情' },
        success: function(response) {
            if (response.status === 'ok') {
                currentSupportData = response.data;
                // 只导入第一个匹配的数据内容（参考微信文章编辑器）
                if (response.data && response.data.length > 0) {
                    const firstSupport = response.data[0];
                    currentSupportId = firstSupport.id;
                    $.cookie('support_content_id', currentSupportId, { expires: 1 });
                    updateSupportPageInfo(firstSupport);

                    console.log('通过标题加载的第一个支持内容:', firstSupport);
                }
            } else {
                show_gitter('错误', response.msg || '加载支持内容失败', 2);
            }
        },
        error: function() {
            show_gitter('错误', '网络错误，加载支持内容失败', 2);
        }
    });
}

/*初始化数据 - 支持内容ID和标题两种方式，优先使用标题（参考微信文章编辑器）*/
function initSupportData(instance) {
    var support_id = $.cookie('support_content_id');
    var support_title = $.cookie('support_content_title');

    console.log('=== initSupportData函数调试信息 ===');
    console.log('Cookie中的support_id:', support_id);
    console.log('Cookie中的support_title:', support_title);
    console.log('instance:', instance);
    console.log('当前全局变量 currentSupportTitle:', currentSupportTitle);
    console.log('当前全局变量 currentSupportId:', currentSupportId);

    // 优先使用标题，如果没有则使用支持内容ID（参考微信文章编辑器的逻辑）
    if (currentSupportTitle) {
        console.log('使用全局变量中的支持内容标题加载数据:', currentSupportTitle);
        loadBySupportTitle(currentSupportTitle, instance);
    } else if (support_title && support_title !== '') {
        console.log('使用Cookie中的支持内容标题加载数据:', support_title);
        loadBySupportTitle(support_title, instance);
    } else if (currentSupportId && !isNaN(currentSupportId)) {
        console.log('使用全局变量中的支持内容ID加载数据:', currentSupportId);
        loadBySupportId(currentSupportId, instance);
    } else if (support_id && support_id !== '' && !isNaN(support_id)) {
        console.log('使用Cookie中的支持内容ID加载数据:', support_id);
        loadBySupportId(parseInt(support_id), instance);
    } else {
        console.log('错误：initSupportData函数中没有找到支持内容ID或标题');
        show_gitter('提示信息', '信息过期，请返回重新进入', 2);
        self.location = '/admin/support_content.html'
        return
    }
    console.log('========================');
}

// 通过支持内容ID加载数据（完全参考微信文章编辑器）
function loadBySupportId(support_id, instance) {
    var html_content = '';

    $.ajax({
        type: "get",
        url: "/apis/support_detail/",
        async: false,
        data: { id: support_id },
        success: function (data) {
            if (data.status === 'ok') {
                var supportData = data.data;

                // 直接使用html_content字段，如果没有则构建（参考微信文章编辑器）
                if (supportData.html_content && supportData.html_content.trim()) {
                    html_content = supportData.html_content;
                    console.log('使用已保存的HTML内容');
                } else {
                    // 如果没有HTML内容，构建包含标题的HTML内容
                    html_content = buildContentWithTitle(supportData.title, supportData.content);
                    console.log('构建包含标题的HTML内容');
                }

                console.log('通过ID加载的支持内容:', supportData.title);
                console.log('HTML内容长度:', html_content.length);

                // 更新页面标题和信息
                updateSupportPageInfo(supportData);
            } else {
                var err_msg = data.msg ? data.msg : '内部服务错误';
                show_gitter('错误提示', err_msg, 2);
            }
        },
        error: function() {
            show_gitter('错误提示', '网络错误，加载支持内容失败', 2);
        }
    });

    if (instance != null) {
        tinyMCE.activeEditor.setContent(html_content);
    }
    $('#old_content').val(html_content);
}

// 通过支持内容标题加载数据（完全参考微信文章编辑器，只导入第一个匹配的数据）
function loadBySupportTitle(support_title, instance) {
    console.log('=== loadBySupportTitle函数调试信息 ===');
    console.log('请求的标题:', support_title);
    console.log('请求URL: /apis/get_support_by_title/');

    var html_content = '';
    $.ajax({
        type: "post",
        url: "/apis/get_support_by_title/",
        async: false,
        data: {title: support_title, type: '详情'},
        success: function (data) {
            console.log('服务器响应:', data);
            if (data.status === 'ok') {
                var data_list = data.data;
                console.log('获取到的数据:', data_list);
                if (data_list.length > 0) {
                    // 只导入第一个匹配的数据内容（参考微信文章编辑器）
                    const firstSupport = data_list[0];

                    // 直接使用html_content字段（参考微信文章编辑器）
                    html_content = firstSupport.html_content || '';
                    console.log('导入第一个支持内容:', firstSupport.title);
                    console.log('HTML内容长度:', html_content.length);

                    // 更新页面标题和信息（显示实际的标题，而不是搜索的标题）
                    updateSupportPageInfo(firstSupport);

                    // 保存支持内容ID到Cookie以便后续操作
                    if (firstSupport.id) {
                        $.cookie('support_content_id', firstSupport.id, { expires: 1 });
                        currentSupportId = firstSupport.id;
                        console.log('保存支持内容ID到Cookie:', firstSupport.id);
                    }
                }
            } else {
                var err_msg = data.msg ? data.msg : '内部服务错误';
                console.log('服务器错误:', err_msg);
                show_gitter('错误提示', err_msg, 2);
            }
        },
        error: function(xhr, status, error) {
            console.log('AJAX请求失败:', error);
            console.log('状态:', status);
            console.log('响应:', xhr.responseText);
            show_gitter('错误提示', '网络错误，加载支持内容失败', 2);
        }
    });

    if (instance != null) {
        tinyMCE.activeEditor.setContent(html_content);
    }
    $('#old_content').val(html_content);
    console.log('========================');
}

// 更新支持内容页面信息（参考微信文章编辑器的显示方式）
function updateSupportPageInfo(data) {
    // 更新面包屑（显示实际的标题）
    $('#support-name').text(data.title || '技术支持内容详情');

    // 显示支持内容信息
    $('#support-info').show();
    $('#support-title').text(data.title || '无标题');
    $('#support-id').text(data.id || '');
    $('#support-type').text('详情');
    $('#created-time').text(formatDateTime(data.created_at || data.update_time));

    // 设置状态选择器
    $('#sel_status').val(data.show || 1);

    // 设置隐藏字段
    $('#current-support-id').val(data.id || '');

    // 设置预览模态框的默认值（使用实际的标题）
    $('#preview-title').val(data.title || '');

    console.log('页面信息已更新:', {
        title: data.title,
        id: data.id,
        show: data.show,
        created_at: data.created_at
    });
}

// 保存支持内容（完全参考微信文章的保存方式）
function update_detail() {
    if (!currentSupportId) {
        show_gitter('错误', '缺少支持内容ID', 3);
        return;
    }

    // 获取编辑器内容
    const editor = tinymce.get('tinymce_demo');
    if (!editor) {
        show_gitter('错误', '编辑器未初始化', 3);
        return;
    }

    var html_content = editor.getContent();
    const status = $('#sel_status').val();

    if (!html_content.trim()) {
        show_gitter('提示信息', '内容不能为空', 2);
        return;
    }

    // 提取标题（从HTML内容中提取第一个h1-h6标签的内容）
    var extractedTitle = extractTitleFromHtml(html_content);
    var currentTitle = $('#support-title').text();
    var titleToSave = extractedTitle || currentTitle || '无标题';

    // 提取纯内容（移除标题后的内容）
    var contentWithoutTitle = extractContentWithoutTitle(html_content);

    const formData = {
        id: currentSupportId,
        title: titleToSave,                    // 保存提取的标题
        content: contentWithoutTitle,          // 保存纯内容（向后兼容）
        html_content: html_content,            // 保存完整的HTML内容（参考微信文章编辑器）
        show: parseInt(status),
        type: '详情'
    };

    console.log('准备保存支持内容:', formData);
    console.log('提取的标题:', titleToSave);
    console.log('HTML内容长度:', html_content.length);
    console.log('纯内容长度:', contentWithoutTitle.length);

    $.ajax({
        type: "post",
        url: "/apis/update_support_html/",
        data: formData,
        success: function (data) {
            if (data.status === 'ok') {
                // 更新旧内容字段（保存完整内容用于比较）
                $('#old_content').val(html_content);
                show_gitter('成功', '支持内容保存成功', 1);
                console.log('支持内容保存成功');

                // 保存中文内容成功后，开始翻译并保存英文内容
                translateAndSaveEnglishContent(formData, currentSupportId);
            } else {
                var err_msg = data.msg ? data.msg : '保存失败';
                show_gitter('错误', err_msg, 3);
            }
        },
        error: function() {
            show_gitter('错误', '网络错误，保存失败', 3);
        }
    });
}

// 生成预览页面
function generateSupportPage() {
    $('#makeHtml').modal('show');
}

// 生成支持内容HTML
function make_support_html() {
    const title = $('#preview-title').val().trim();
    const type = $('#preview-type').val().trim();
    const lang = $('#preview-lang').val();

    if (!title) {
        alert('请输入内容标题');
        return;
    }

    const editor = tinymce.get('tinymce_demo');
    if (!editor) {
        alert('编辑器未初始化');
        return;
    }

    const content = editor.getContent();
    if (!content.trim()) {
        alert('请输入内容');
        return;
    }

    console.log('生成预览页面:', { title, type, lang, content });

    // 调用生成页面的API
    $.ajax({
        type: "post",
        url: "/apis/generate_support_page/",
        data: {
            title: title,
            type: type,
            lang: lang,
            content: content,
            support_id: currentSupportId
        },
        success: function (data) {
            if (data.status === 'ok') {
                $('#makeHtml').modal('hide');
                show_gitter('成功', '预览页面生成成功', 1);
            } else {
                var err_msg = data.msg ? data.msg : '生成失败';
                show_gitter('错误', err_msg, 3);
            }
        },
        error: function() {
            show_gitter('错误', '网络错误，生成失败', 3);
        }
    });
}

// 优化图片
function replaceSupportImages() {
    const editor = tinymce.get('tinymce_demo');
    if (!editor) {
        show_gitter('错误', '编辑器未初始化', 3);
        return;
    }

    const content = editor.getContent();

    $.ajax({
        type: "post",
        url: "/apis/optimize_support_images/",
        data: {
            content: content,
            support_id: currentSupportId
        },
        success: function (data) {
            if (data.status === 'ok') {
                // 更新编辑器内容
                editor.setContent(data.optimized_content);
                show_gitter('成功', '图片优化完成', 1);
            } else {
                var err_msg = data.msg ? data.msg : '优化失败';
                show_gitter('错误', err_msg, 3);
            }
        },
        error: function() {
            show_gitter('错误', '网络错误，优化失败', 3);
        }
    });
}

// 格式化日期时间
function formatDateTime(dateString) {
    if (!dateString) return '';

    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}`;
}

// 构建包含标题的HTML内容（参考微信文章编辑器）
function buildContentWithTitle(title, content) {
    var html_content = '';

    // 如果有标题，添加到内容开头
    if (title && title.trim()) {
        // 检查内容是否已经包含了这个标题
        var titlePattern = new RegExp('<h[1-6][^>]*>' + title.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') + '</h[1-6]>', 'i');
        if (!titlePattern.test(content)) {
            // 如果内容中没有包含标题，则添加标题
            html_content = `<h2>${title}</h2>`;
            console.log('添加标题到内容开头:', title);
        } else {
            console.log('内容中已包含标题，不重复添加');
        }
    }

    // 添加原始内容
    if (content && content.trim()) {
        html_content += content;
    }

    console.log('构建的完整HTML内容:', html_content.substring(0, 200) + '...');
    return html_content;
}

// 从HTML内容中提取标题
function extractTitleFromHtml(htmlContent) {
    if (!htmlContent || !htmlContent.trim()) {
        return '';
    }

    // 匹配第一个h1-h6标签的内容
    var titleMatch = htmlContent.match(/<h[1-6][^>]*>(.*?)<\/h[1-6]>/i);
    if (titleMatch && titleMatch[1]) {
        // 移除HTML标签，只保留文本内容
        var title = titleMatch[1].replace(/<[^>]*>/g, '').trim();
        console.log('从HTML中提取的标题:', title);
        return title;
    }

    console.log('未从HTML中找到标题');
    return '';
}

// 提取纯内容（移除标题部分）
function extractContentWithoutTitle(fullContent) {
    if (!fullContent || !fullContent.trim()) {
        return '';
    }

    // 移除第一个h1-h6标签（标题）
    var contentWithoutTitle = fullContent.replace(/^\s*<h[1-6][^>]*>.*?<\/h[1-6]>\s*/i, '');

    console.log('移除标题后的内容长度:', contentWithoutTitle.length);
    return contentWithoutTitle;
}

// 显示Gritter通知
function show_gitter(title, message, type) {
    const typeMap = {
        1: 'success',
        2: 'warning',
        3: 'error'
    };

    $.gritter.add({
        title: title,
        text: message,
        class_name: typeMap[type] || 'info',
        time: 3000
    });
}

// 翻译并保存英文内容
async function translateAndSaveEnglishContent(chineseData, chineseId) {
    try {
        console.log('开始翻译并保存英文内容...');
        show_gitter('信息', '正在翻译英文内容...', 1);

        // 翻译标题和内容（完全按照server.js中微信文章的方式）
        console.log('=== 开始翻译Support内容 ===');
        console.log('原始标题:', chineseData.title);
        console.log('原始HTML内容长度:', chineseData.html_content ? chineseData.html_content.length : 0);

        const translatedTitle = await translateTextWithGoogle(chineseData.title);
        console.log('翻译后标题:', translatedTitle);

        const translatedContent = await translateHtmlContentWithGoogle(chineseData.html_content);
        console.log('=== 翻译后的完整HTML源码 ===');
        console.log(translatedContent);
        console.log('=== HTML源码结束 ===');

        // 获取中文记录的display_order
        const chineseRecord = await getCurrentSupportRecord(chineseId);
        const displayOrder = chineseRecord ? chineseRecord.display_order : 100;

        // 准备英文数据（完全按照server.js中微信文章的方式）
        const englishData = {
            type: chineseData.type,
            title: translatedTitle,
            content: '', // 不需要单独的content字段，内容都在html_content中
            html_content: translatedContent, // 这里是翻译后的完整HTML内容
            display_order: displayOrder, // 使用与中文记录相同的显示顺序
            show: chineseData.show,
            lang: 1, // 英文
            source_id: chineseId // 关联中文记录ID
        };

        console.log('=== 准备保存的英文数据 ===');
        console.log('标题:', englishData.title);
        console.log('HTML内容长度:', englishData.html_content ? englishData.html_content.length : 0);
        console.log('HTML内容预览:', englishData.html_content ? englishData.html_content.substring(0, 200) + '...' : '空');
        console.log('完整英文数据:', englishData);

        // 检查是否已存在对应的英文记录
        const existingEnglishId = await findExistingEnglishRecord(chineseId);

        if (existingEnglishId) {
            // 更新现有英文记录
            englishData.id = existingEnglishId;
            await saveEnglishRecord('/apis/update_support/', englishData);
            console.log('英文内容更新成功');
            show_gitter('成功', '英文内容更新成功', 1);
        } else {
            // 创建新的英文记录
            await saveEnglishRecord('/apis/add_support/', englishData);
            console.log('英文内容创建成功');
            show_gitter('成功', '英文内容创建成功', 1);
        }

    } catch (error) {
        console.error('翻译和保存英文内容失败:', error);
        show_gitter('警告', '英文内容保存失败: ' + error.message, 2);
    }
}







// 翻译HTML内容（完全按照server.js中translateHtmlContentWithGoogle的实现）
async function translateHtmlContentWithGoogle(htmlContent) {
    if (!htmlContent || !htmlContent.trim()) {
        console.log('HTML内容为空，跳过翻译');
        return htmlContent || '';
    }

    try {
        console.log('开始使用Google翻译API翻译HTML内容...');
        console.log('原始HTML内容:', htmlContent.substring(0, 200) + '...');

        // 简单的文本提取和翻译方法
        const textRegex = />([^<]*[\u4e00-\u9fa5][^<]*)</g;
        const textsToTranslate = [];
        let match;

        // 提取所有包含中文的文本
        while ((match = textRegex.exec(htmlContent)) !== null) {
            const text = match[1].trim();
            if (text && !textsToTranslate.includes(text)) {
                textsToTranslate.push(text);
            }
        }

        console.log(`需要翻译 ${textsToTranslate.length} 个文本片段:`, textsToTranslate);

        let translatedHtml = htmlContent;

        // 逐个翻译文本片段
        for (const text of textsToTranslate) {
            try {
                const response = await fetch('/api/translate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        text: text,
                        from: 'zh',
                        to: 'en'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.translatedText) {
                        // 安全的替换逻辑 - 允许翻译错误，不会导致语法错误
                        if (translatedHtml.includes(text)) {
                            try {
                                // 直接替换，不做复杂的转义处理
                                translatedHtml = translatedHtml.split(text).join(data.translatedText);
                            } catch (error) {
                                // 如果替换失败，跳过这个翻译，继续处理其他文本
                                console.warn(`替换文本失败，跳过: ${text.substring(0, 30)}...`);
                            }
                        }
                        console.log(`翻译成功: ${text.substring(0, 30)}... -> ${data.translatedText.substring(0, 30)}...`);
                    } else {
                        console.warn(`翻译失败: ${text.substring(0, 30)}...`);
                    }
                } else {
                    console.warn(`翻译请求失败: ${text.substring(0, 30)}...`);
                }

                // 添加延迟避免请求过快
                await new Promise(resolve => setTimeout(resolve, 200));
            } catch (error) {
                console.error(`翻译单个文本失败: ${text.substring(0, 30)}...`, error);
            }
        }

        console.log('HTML内容翻译完成');
        return translatedHtml;

    } catch (error) {
        console.error('Google翻译HTML内容失败:', error);
        return htmlContent; // 返回原始内容
    }
}

// 使用Google翻译API翻译文本（完全按照server.js中translateTextWithGoogle的实现）
async function translateTextWithGoogle(text) {
    if (!text || !text.trim()) {
        console.log('文本为空，跳过翻译');
        return text || '';
    }

    try {
        console.log('开始翻译文本:', text);
        const response = await fetch('/api/translate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                text: text,
                from: 'zh',
                to: 'en'
            })
        });

        if (response.ok) {
            const data = await response.json();
            if (data.success && data.translatedText) {
                console.log(`文本翻译成功: ${text} -> ${data.translatedText}`);
                return data.translatedText;
            } else {
                console.warn(`翻译API返回失败:`, data);
            }
        } else {
            console.warn(`翻译请求失败，状态码:`, response.status);
        }

        console.warn(`文本翻译失败，使用原文: ${text}`);
        return text;
    } catch (error) {
        console.error('翻译文本失败:', error);
        return text;
    }
}



// 转义正则表达式特殊字符
function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

// 获取当前支持记录
async function getCurrentSupportRecord(supportId) {
    try {
        const response = await $.ajax({
            url: '/apis/support_detail/',
            type: 'GET',
            data: {
                id: supportId
            }
        });

        if (response.status === 'ok' && response.data) {
            return response.data;
        }

        return null;
    } catch (error) {
        console.warn('获取当前支持记录失败:', error);
        return null;
    }
}

// 获取当前支持记录
async function getCurrentSupportRecord(supportId) {
    try {
        const response = await $.ajax({
            url: '/apis/support_detail/',
            type: 'GET',
            data: {
                id: supportId
            }
        });

        if (response.status === 'ok' && response.data) {
            return response.data;
        }

        return null;
    } catch (error) {
        console.warn('获取当前支持记录失败:', error);
        return null;
    }
}

// 查找现有的英文记录
async function findExistingEnglishRecord(chineseId) {
    try {
        const response = await $.ajax({
            url: '/apis/support_list/',
            type: 'GET',
            data: {
                lang: 1, // 英文
                source_id: chineseId
            }
        });

        if (response.status === 'ok' && response.data && response.data.length > 0) {
            return response.data[0].id;
        }

        return null;
    } catch (error) {
        console.warn('查找现有英文记录失败:', error);
        return null;
    }
}

// 保存英文记录
async function saveEnglishRecord(url, data) {
    console.log('=== 开始保存英文记录 ===');
    console.log('保存URL:', url);
    console.log('发送的数据:', data);
    console.log('HTML内容长度:', data.html_content ? data.html_content.length : 0);

    return new Promise((resolve, reject) => {
        $.ajax({
            url: url,
            type: 'POST',
            data: data,
            success: function(response) {
                console.log('保存英文记录响应:', response);
                if (response.status === 'ok') {
                    console.log('英文记录保存成功');
                    resolve(response);
                } else {
                    console.error('保存失败:', response);
                    reject(new Error(response.message || response.msg || '保存失败'));
                }
            },
            error: function(xhr, status, error) {
                console.error('保存请求失败:', xhr, status, error);
                reject(new Error(error));
            }
        });
    });
}
</script>
</body>
</html>