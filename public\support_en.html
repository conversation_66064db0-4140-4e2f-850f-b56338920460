<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>RK3568 Data Acquisition Gateway-Service & Support-Bearkey-Official Website</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    
    <!-- css -->
    <link href="./css/bootstrap.min.css" rel="stylesheet"/>
    <link href="./css/style.css" rel="stylesheet"/>
    <link href="./css/footer.css" rel="stylesheet"/>
    <link href="./css/support-page.css" rel="stylesheet"/>
    <!-- Font Awesome 图标库 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet">
</head>
<body>
<div id="wrapper" style="display: none;">
    <!-- 导航栏 -->
    <header>
        <!-- 导航栏将通过nav.js动态加载 -->
    </header>

    <!-- 页面标题背景板 -->
    <div class="page-title-banner">
        <div class="container">
            <h1 class="page-title">Service & Support</h1>
        </div>
    </div>

    <div class="containert">
        <!-- 面包屑导航 -->
        <div class="breadcrumb">
            <a href="/">Bearkey Official Website</a>
            <span class="separator">/</span>
            <span class="current">Service & Support</span>
        </div>

        <!-- 主要内容区域 -->
        <div class="support-main">
            <!-- 左侧选择区域 -->
            <div class="support-sidebar">
                <!-- 菜单将通过JavaScript动态加载 -->
            </div>

            <!-- 右侧内容区域 -->
            <div class="support-content" id="support-content">
                <!-- 这里直接显示导入的HTML内容 -->
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <!-- 页脚将通过support.js动态加载 -->
</div>
<a href="#" class="scrollup"><i class="fa fa-angle-up active"></i></a>

<!-- javascript -->
<script src="js/jquery.js"></script>
<script src="js/jquery.easing.1.3.js"></script>
<script src="js/bootstrap.min.js"></script>

<script src="js/nav.js"></script>
<script src="js/floating-toolbar.js"></script>
<script src="js/toolbar-data.js"></script>
<script src="js/support.js"></script>
<script src="js/note-text-formatter.js"></script>
<script>
// 返回置顶功能
$(document).ready(function() {
    // 监听滚动事件
    $(window).scroll(function(){
        if ($(this).scrollTop() > 100) {
            $('.scrollup').fadeIn();
        } else {
            $('.scrollup').fadeOut();
        }
    });

    // 点击返回顶部
    $('.scrollup').click(function(){
        $("html, body").animate({ scrollTop: 0 }, 1000);
        return false;
    });
});
</script>

<script>
// 全局菜单状态保存函数
function saveMenuState(menuId, submenuId) {
    localStorage.setItem('supportMenuState', JSON.stringify({
        menuId: menuId,
        submenuId: submenuId,
        timestamp: Date.now()
    }));
}
</script>
</body>
</html>
